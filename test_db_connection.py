#!/usr/bin/env python3
"""
Test script to verify SingleStore database connection
"""
import os
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_connection():
    """Test database connection with current credentials"""

    # Get credentials from environment
    host = os.getenv("SINGLESTORE_HOST")
    port = int(os.getenv("SINGLESTORE_PORT", "3306"))
    user = os.getenv("SINGLESTORE_USER")
    password = os.getenv("SINGLESTORE_PASSWORD")
    database = os.getenv("SINGLESTORE_DATABASE")

    print("=== SingleStore Connection Test ===")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"User: {user}")
    print(f"Password: {'*' * len(password) if password else 'NOT SET'}")
    print(f"Database: {database}")
    print()

    # Check if all required variables are set
    if not all([host, user, password, database]):
        print("❌ ERROR: Missing required environment variables!")
        missing = []
        if not host: missing.append("SINGLESTORE_HOST")
        if not user: missing.append("SINGLESTORE_USER")
        if not password: missing.append("SINGLESTORE_PASSWORD")
        if not database: missing.append("SINGLESTORE_DATABASE")
        print(f"Missing: {', '.join(missing)}")
        return False

    # Test different connection configurations
    configs = [
        {
            "name": "Standard Connection",
            "config": {
                "host": host,
                "port": port,
                "user": user,
                "password": password,
                "database": database,
                "connect_timeout": 10
            }
        },
        {
            "name": "With SSL",
            "config": {
                "host": host,
                "port": port,
                "user": user,
                "password": password,
                "database": database,
                "connect_timeout": 10,
                "ssl_disabled": False
            }
        },
        {
            "name": "Without SSL",
            "config": {
                "host": host,
                "port": port,
                "user": user,
                "password": password,
                "database": database,
                "connect_timeout": 10,
                "ssl_disabled": True
            }
        },
        {
            "name": "Raw Password (no quotes)",
            "config": {
                "host": host,
                "port": port,
                "user": user,
                "password": password.strip('"\''),  # Remove quotes if any
                "database": database,
                "connect_timeout": 10
            }
        }
    ]

    for config_test in configs:
        print(f"\n🔄 Testing: {config_test['name']}")
        try:
            connection = mysql.connector.connect(**config_test['config'])

            if connection.is_connected():
                print(f"✅ {config_test['name']} - Connection successful!")

                # Get database info
                cursor = connection.cursor()

                # Test basic query
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"Database Version: {version[0]}")

                # Get current user and database
                cursor.execute("SELECT USER(), DATABASE()")
                user_db = cursor.fetchone()
                print(f"Connected as: {user_db[0]}")
                print(f"Current database: {user_db[1]}")

                cursor.close()
                connection.close()
                print("✅ Connection test completed successfully!")
                return True

        except Error as e:
            print(f"❌ {config_test['name']} failed!")
            print(f"Error Code: {e.errno}")
            print(f"Error Message: {e.msg}")

            # Provide specific guidance based on error
            if e.errno == 1045:
                print("🔍 Authentication error - check credentials and IP whitelist")
            elif e.errno == 2003:
                print("🔍 Connection error - check host/port and network")

        except Exception as e:
            print(f"❌ {config_test['name']} - Unexpected error: {str(e)}")

    return False

if __name__ == "__main__":
    success = test_connection()
    exit(0 if success else 1)
