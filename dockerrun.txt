ddocker run \
    -d --name singlestoredb-dev \
    -e ROOT_PASSWORD="2t]]zmczxuslsNuw31zg_e0M5" \
    -p 3306:3306 -p 8080:8080 -p 9000:9000 \
    -v singlestoreData:/data \
    ghcr.io/singlestore-labs/singlestoredb-dev




mysql -h 127.0.0.1 -P 3306 -u user_8dd7b -p'2t]]zmczxuslsNuw31zg_e0M5' db_rag
CREATE DATABASE db_rag;
CREATE USER 'user_8dd7b' IDENTIFIED BY '2t]]zmczxuslsNuw31zg_e0M5';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, DROP, INDEX ON db_rag.* TO 'user_8dd7b';